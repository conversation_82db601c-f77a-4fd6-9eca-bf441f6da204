# import datetime
from fastapi import APIRouter, File, Request, UploadFile, HTTPException, Depends
from botocore.exceptions import NoCredentialsError, ClientError
from fastapi.responses import JSONResponse
from uuid import uuid4
from src.globals import AWS_S3_BUCKET_NAME, AWS_S3_REGION
from src.users.models import UserRecordModel
from src.auth.api import get_current_user

# from pprint import pprint
# from datetime import datetime


# Load environment variables
from dotenv import load_dotenv

load_dotenv()

media_img_router = APIRouter()

# region_name=AWS_S3_REGION, )


def get_user_img_dir(user: UserRecordModel):
    return f"{user.id}/img/"


@media_img_router.post("/upload/")
async def upload_image(
    request: Request,
    file: UploadFile = File(...),
    user: UserRecordModel = Depends(get_current_user),
):
    upload_image()

@media_img_router.put("/edit/")
async def edit_image(
    request: Request,
    image_key: str,
    file: UploadFile = File(...),
    user: UserRecordModel = Depends(get_current_user),
):
    if not image_key.find(user.id):
        raise HTTPException(
            status_code=403, detail="Non-owner cannot delete another user's " "images."
        )
    request.app.s3_client.upload_fileobj(file.file, AWS_S3_BUCKET_NAME, image_key)
    return {"message": "Image edited successfully"}


@media_img_router.delete("/delete/")
async def delete_image(
    request: Request, image_key: str, user: UserRecordModel = Depends(get_current_user)
):
    if not image_key.find(user.id):
        raise HTTPException(
            status_code=403, detail="Non-owner cannot delete another user's " "images."
        )
    response = request.app.s3_client.delete_object(
        Bucket=AWS_S3_BUCKET_NAME, Key=image_key
    )  # noqa
    # pprint(response)
    return {"message": "Image deleted successfully"}


@media_img_router.post("/batch/upload/")
async def batch_upload(
    request: Request,
    files: list[UploadFile] = File(...),
    user: UserRecordModel = Depends(get_current_user),
):
    responses = []
    for file in files:
        file_name = "users/" + get_user_img_dir(user) + str(uuid4()) + file.filename
        # s3_key = f"{datetime.now(datetime.UTC).isoformat()}-{uuid4()}.jpg"
        # request.app.s3_client.upload_fileobj(file.file, AWS_S3_BUCKET_NAME, s3_key)
        request.app.s3_client.upload_fileobj(file.file, AWS_S3_BUCKET_NAME, file_name)
        responses.append({"key": file_name})
    return {"message": "Batch upload successful", "files": responses}


@media_img_router.post("/batch/delete/")
async def batch_delete(
    request: Request, keys: list[str], user: UserRecordModel = Depends(get_current_user)
):
    objects = [{"Key": key} for key in keys]
    if [not key.find(user.id) for key in keys]:
        raise HTTPException(
            status_code=403, detail="Non-owner cannot delete another user's " "images."
        )

    request.app.s3_client.delete_objects(
        Bucket=AWS_S3_BUCKET_NAME, Delete={"Objects": objects}
    )
    return {"message": "Batch delete successful"}


# if __name__ == "__main__":
# print(request.app.s3_client.profile_name)
# print(str(request.app.s3_client.list_buckets()))
